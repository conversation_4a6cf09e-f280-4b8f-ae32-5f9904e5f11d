package edu.odu.cs.cs361;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PermittedResearch {


    boolean couldBeResearched(ResearchPlan plan,
            List<String> alreadyKnown) {
        for (int i = 0; i < plan.getNumberOfRequirements(); ++i) {
            Topic required = plan.getRequirement(i);
            if (!alreadyKnown.contains(required.getName())) {
                return false; // If any required prior topic is unknown.
            }
        }
        return true; // If every required prior topic is already known.
    }

    /**
     * Print a list of the research topics that could be researched
     * by a game player, given the encyclopedia of possible research plans
     * and the list of topics already known/researched.
     * @throws NoSuchPlan 
     */
    void permittedResearch(Encyclopedia encyclopedia,
            List<String> alreadyKnown) throws NoSuchPlan {
        List<Topic> researchable = new ArrayList<>();
        Topic lowestCostTopic = new Topic("nonsense", Integer.MAX_VALUE);
        for (Topic topic : encyclopedia) {
            ResearchPlan plan = encyclopedia.getPlan(topic);
            if (!alreadyKnown.contains(plan.getTopic().getName())
                    && couldBeResearched(plan, alreadyKnown)) {
                researchable.add(topic);
                if (topic.getResearchCost() < lowestCostTopic.getResearchCost()) {
                    lowestCostTopic = topic;
                }
            }
        }
        System.out.println("There are " + researchable.size()
                + " topics available for research.");
        Collections.sort(researchable, (t, u) -> {
            return t.compareTo(u);
        });
        for (Topic topic : researchable) {
            System.out.println(String.format("%24s ", topic.getName()) + topic.getResearchCost());
        }
        System.out.println("The cheapest to research is " + lowestCostTopic);
    }

    public void run(BufferedReader in) throws NoSuchPlan, IOException {
        Encyclopedia encyclopedia = new Encyclopedia();
        List<String> alreadyResearched = new ArrayList<>();
        encyclopedia.read(in);
        String line = in.readLine();
        while (line != null && !line.equals("---")) {
            alreadyResearched.add(line.trim());
            line = in.readLine();
        }
        in.close();
        permittedResearch(encyclopedia, alreadyResearched);
    }

    public static void main(String[] args) throws IOException, NoSuchPlan {
        BufferedReader in;
        if (args.length > 0) {
            in = new BufferedReader(new FileReader(args[0]));
        } else {
            in = new BufferedReader(new InputStreamReader(System.in));
        }
        new PermittedResearch().run(in);
        in.close();
    }
}