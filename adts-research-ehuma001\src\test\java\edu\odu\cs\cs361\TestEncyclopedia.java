/**
 * 
 */
package edu.odu.cs.cs361;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.StringReader;


//import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

/**
 * <AUTHOR>
 *
 */
public class TestEncyclopedia {

    @Test public void testEncyclopediaConstructor() {
        Encyclopedia enc = new Encyclopedia();
        assertThat (enc.iterator().hasNext(), equalTo(false));
    }
    
    
    @Test
    public void testEncyclopediaAddCourse() throws NoSuchPlan {
        Topic alphabet = new Topic("Alphabet", 15);
        Topic commerce = new Topic("Commerce", 25);
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        Encyclopedia enc = new Encyclopedia();
        enc.addPlanRequirement(commerce, alphabet);
        assertThat (enc.size(), equalTo(2));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertFalse(enc.containsTopic(sailing));
        assertFalse(enc.containsTopic(mapping));
        assertFalse(enc.containsTopic(trading));
    
        assertThat(enc, containsInAnyOrder(alphabet, commerce));
    
        enc.addPlanRequirement(trading, sailing);
        assertThat (enc.size(), equalTo(4));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertTrue(enc.containsTopic(sailing));
        assertFalse(enc.containsTopic(mapping));
        assertTrue(enc.containsTopic(trading));
    
        assertThat(enc, containsInAnyOrder(alphabet, commerce, sailing, trading));
    
        enc.addPlanRequirement(trading, mapping);
        assertThat (enc.size(), equalTo(5));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertTrue(enc.containsTopic(sailing));
        assertTrue(enc.containsTopic(mapping));
        assertTrue(enc.containsTopic(trading));
    
        enc.addPlanRequirement(mapping, commerce);
        assertThat (enc.size(), equalTo(5));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertTrue(enc.containsTopic(sailing));
        assertTrue(enc.containsTopic(mapping));
        assertTrue(enc.containsTopic(trading));
    
        enc.addPlanRequirement(sailing, commerce);
        assertThat (enc.size(), equalTo(5));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertTrue(enc.containsTopic(sailing));
        assertTrue(enc.containsTopic(mapping));
        assertTrue(enc.containsTopic(trading));
    
        assertThat(enc, containsInAnyOrder(alphabet, commerce, mapping, sailing, trading));
    
        ResearchPlan plan = enc.getPlan(commerce);
        assertThat (plan.getTopic(), equalTo(commerce));
        assertThat(plan.getNumberOfRequirements(), equalTo(1));
    
        plan = enc.getPlan(trading);
        assertThat (plan.getTopic(), equalTo(trading));
        assertThat(plan.getNumberOfRequirements(), equalTo(2));
    }
    
    
    
    
    @Test 
    public void EncyclopediaRemoveCourse() throws NoSuchPlan {
        Topic alphabet = new Topic("Alphabet", 15);
        Topic commerce = new Topic("Commerce", 25);
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        Encyclopedia enc = new Encyclopedia();
        enc.addPlanRequirement(commerce, alphabet);
        enc.addPlanRequirement(trading, sailing);
        enc.addPlanRequirement(trading, mapping);
        enc.addPlanRequirement(mapping, commerce);
        enc.addPlanRequirement(sailing, commerce);
    
        enc.removeTopic(mapping);
    
        assertThat (enc.size(), equalTo(4));
        assertTrue(enc.containsTopic(alphabet));
        assertTrue(enc.containsTopic(commerce));
        assertTrue(enc.containsTopic(sailing));
        assertFalse(enc.containsTopic(mapping));
        assertTrue(enc.containsTopic(trading));
    
        ResearchPlan plan = enc.getPlan(trading);
        assertThat (plan.getTopic(), equalTo(trading));
        assertThat(plan.getNumberOfRequirements(), equalTo(1));
    }
    
    @Test
    public void EncyclopediaCopy() throws NoSuchPlan {
        Topic alphabet = new Topic("Alphabet", 15);
        Topic commerce = new Topic("Commerce", 25);
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        Encyclopedia enc = new Encyclopedia();
        enc.addPlanRequirement(commerce, alphabet);
        enc.addPlanRequirement(trading, sailing);
        enc.addPlanRequirement(trading, mapping);
        enc.addPlanRequirement(mapping, commerce);
        enc.addPlanRequirement(sailing, commerce);
    
        Encyclopedia enc2 = (Encyclopedia)enc.clone();
    
        assertThat(enc2, equalTo(enc));
    
        enc.removeTopic(mapping);
    
        assertThat(enc2, not(enc));
    
        assertThat (enc2.size(), equalTo(5));
        assertTrue(enc2.containsTopic(alphabet));
        assertTrue(enc2.containsTopic(commerce));
        assertTrue(enc2.containsTopic(sailing));
        assertTrue(enc2.containsTopic(mapping));
        assertTrue(enc2.containsTopic(trading));
    
        ResearchPlan plan = enc2.getPlan(trading);
        assertThat (plan.getTopic(), equalTo(trading));
        assertThat(plan.getNumberOfRequirements(), equalTo(2));
    }
    
    
    
    
    
    @Test
    public void EncyclopediaRead() throws IOException, NoSuchPlan {
        Topic agriculture = new Topic("Agriculture", 10);
        Topic alphabet = new Topic("Alphabet", 15);
        Topic printing = new Topic("Printing", 100);
        Topic publishing = new Topic("Publishing", 100);
    
        String testIn =
                "Agriculture: 10\n" +
                "Alphabet: 15\n" +
                "Printing: Alphabet, 100\n" +
                "Publishing: Printing, Agriculture, 100\n"
                + "---";
        BufferedReader in = new BufferedReader(new StringReader(testIn));
        Encyclopedia enc = new Encyclopedia();
        enc.read(in);
    
        assertTrue (enc.containsTopic(agriculture));
        assertTrue (enc.containsTopic(printing));
        assertTrue (enc.containsTopic(publishing));
        assertTrue (enc.containsTopic(alphabet));
    
        assertThat(enc.size(), equalTo(4));
    
        assertThat(enc.getPlan(agriculture).getNumberOfRequirements(), equalTo(0));
        assertThat(enc.getPlan(printing).getNumberOfRequirements(), equalTo(1));
        assertThat(enc.getPlan(publishing).getNumberOfRequirements(), equalTo(2));
    
        assertThat(enc.getPlan(agriculture).getTopic().getResearchCost(), equalTo(10));
        assertThat(enc.getPlan(alphabet).getTopic().getResearchCost(), equalTo(15));
        assertThat(enc.getPlan(printing).getTopic().getResearchCost(), equalTo(100));
        assertThat(enc.getPlan(publishing).getTopic().getResearchCost(), equalTo(100));
    
    }
    
    
}
