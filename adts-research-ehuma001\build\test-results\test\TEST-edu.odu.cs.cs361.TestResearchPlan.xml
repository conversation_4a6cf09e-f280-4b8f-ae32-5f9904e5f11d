<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="edu.odu.cs.cs361.TestResearchPlan" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-09-04T02:26:15" hostname="DESKTOP-TE1C874" time="0.005">
  <properties/>
  <testcase name="ResearchPlanRemovePrior()" classname="edu.odu.cs.cs361.TestResearchPlan" time="0.003"/>
  <testcase name="ResearchPlanCopy()" classname="edu.odu.cs.cs361.TestResearchPlan" time="0.0"/>
  <testcase name="ResearchPlanConstructor()" classname="edu.odu.cs.cs361.TestResearchPlan" time="0.001"/>
  <testcase name="ResearchPlanDefaultConstructor()" classname="edu.odu.cs.cs361.TestResearchPlan" time="0.0"/>
  <testcase name="ResearchPlanAddPrior()" classname="edu.odu.cs.cs361.TestResearchPlan" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
