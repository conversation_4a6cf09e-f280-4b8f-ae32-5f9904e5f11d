Test,Score,Weight,Msgs
"builder: Attempt to build the program.",100,1,"Starting a Gradle Daemon, 13 busy and 8 incompatible and 6 stopped Daemons could not be reused, use --status for details
> Task :compileJava UP-TO-DATE
> Task :processResources NO-SOURCE
> Task :classes UP-TO-DATE
> Task :jar UP-TO-DATE

BUILD SUCCESSFUL in 3s
2 actionable tasks: 2 up-to-date

--- end of output ---
--- std error ---

--------"
"test000",100,1,"OK"
"test001",100,1,"OK"
"test002",100,1,"OK"
"test003",100,1,"OK"
"test004",100,1,"OK"
"test005",100,1,"OK"
"unittests",70,3,"Failed test: ResearchPlanAddPrior()
Failed test: ResearchPlanConstructor()
Failed test: ResearchPlanRemovePrior()

on std err:

10 tests completed, 3 failed"
