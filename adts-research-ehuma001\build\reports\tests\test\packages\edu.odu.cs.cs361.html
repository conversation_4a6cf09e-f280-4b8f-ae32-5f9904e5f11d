<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package edu.odu.cs.cs361</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package edu.odu.cs.cs361</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; edu.odu.cs.cs361</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">10</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">3</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.019s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">70%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Failed tests</a>
</li>
<li>
<a href="#tab1">Classes</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html">TestResearchPlan</a>.
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html#ResearchPlanAddPrior()">ResearchPlanAddPrior()</a>
</li>
<li>
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html">TestResearchPlan</a>.
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html#ResearchPlanConstructor()">ResearchPlanConstructor()</a>
</li>
<li>
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html">TestResearchPlan</a>.
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html#ResearchPlanRemovePrior()">ResearchPlanRemovePrior()</a>
</li>
</ul>
</div>
<div id="tab1" class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="success">
<a href="../classes/edu.odu.cs.cs361.TestEncyclopedia.html">TestEncyclopedia</a>
</td>
<td>5</td>
<td>0</td>
<td>0</td>
<td>0.006s</td>
<td class="success">100%</td>
</tr>
<tr>
<td class="failures">
<a href="../classes/edu.odu.cs.cs361.TestResearchPlan.html">TestResearchPlan</a>
</td>
<td>5</td>
<td>3</td>
<td>0</td>
<td>0.013s</td>
<td class="failures">40%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.8</a> at Sep 3, 2025, 10:58:56 PM</p>
</div>
</div>
</body>
</html>
