> Task :compileJava UP-TO-DATE
> Task :processResources NO-SOURCE
> Task :classes UP-TO-DATE
> Task :compileTestJava UP-TO-DATE
> Task :processTestResources NO-SOURCE
> Task :testClasses UP-TO-DATE

> Task :test

TestResearchPlan > ResearchPlanRemovePrior() FAILED
    java.lang.AssertionError at TestResearchPlan.java:100

TestResearchPlan > ResearchPlanConstructor() FAILED
    java.lang.AssertionError at TestResearchPlan.java:36

TestResearchPlan > ResearchPlanAddPrior() FAILED
    java.lang.AssertionError at TestResearchPlan.java:68
There were failing tests. See the report at: file:///C:/Users/<USER>/Desktop/test/adts-research-ehuma001/build/reports/tests/test/index.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.8/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 1s
3 actionable tasks: 1 executed, 2 up-to-date
