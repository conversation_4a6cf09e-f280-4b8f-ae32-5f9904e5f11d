/**
 * 
 */
package edu.odu.cs.cs361;

//import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.*;

/**
 * <AUTHOR>
 *
 */
public class TestResearchPlan {

    @Test
    public void ResearchPlanDefaultConstructor() {
        ResearchPlan plan = new ResearchPlan();
        assertThat (plan.getTopic(), is(new Topic()));
        assertThat (plan.getNumberOfRequirements(), is(0));
        assertThat (plan, equalTo(new ResearchPlan()));
        assertThat (plan.compareTo(new ResearchPlan()), is(0));
    }
    
    @Test
    public void ResearchPlanConstructor() {
        Topic alphabet = new Topic("Alphabet", 15);
        ResearchPlan plan = new ResearchPlan(alphabet);
    
        assertThat (plan.getTopic(), is(alphabet));
        assertThat (plan.getNumberOfRequirements(), is(0));
        assertThat(plan.equals(new ResearchPlan()), is(false));
        assertThat (plan.compareTo(new ResearchPlan()), not(is(0)));
    
        assertThat (plan.toString(), containsString("Alphabet"));
    }
    
    
    @Test
    public void ResearchPlanAddPrior() {
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        ResearchPlan plan0 = new ResearchPlan(trading);
        ResearchPlan plan = new ResearchPlan(trading);
        assertThat (plan, is(plan0));
    
        plan.addRequirement(sailing);
        assertThat (plan.getNumberOfRequirements(), is(1));
        assertThat (plan, not(is(plan0)));
        assertThat(plan.compareTo(plan0), not(is(0)));

        assertThat (plan.getRequirement(0), is(sailing));
    
        plan.addRequirement(mapping);
        assertThat (plan.getNumberOfRequirements(), is(2));
        assertThat (plan.getRequirement(0), is(sailing));
        assertThat (plan.getRequirement(1), is(mapping));
    
        plan.addRequirement(sailing);
        assertThat (plan.getNumberOfRequirements(), is(2));
        assertThat (plan.getRequirement(0), is(sailing));
        assertThat (plan.getRequirement(1), is(mapping));
    
        String outStr = plan.toString();
        assertThat (outStr, containsString("Trading"));
        assertThat (outStr, containsString("Mapping"));
        assertThat (outStr, containsString("Sailing"));
    
    }
    
    @Test
    public void ResearchPlanRemovePrior() {
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        ResearchPlan plan0 = new ResearchPlan(trading);
        ResearchPlan plan = new ResearchPlan(trading);
        assertThat (plan, is(plan0));
    
        plan.addRequirement(sailing);
        assertThat (plan.getNumberOfRequirements(), is(1));
        assertThat (plan, is(not(plan0)));
        assertThat (plan.compareTo(plan0), is(not(0)));
        assertThat (plan.getRequirement(0), is(sailing));
    
        plan.addRequirement(mapping);
        assertThat (plan.getNumberOfRequirements(), is(2));
        assertThat (plan.getRequirement(0), is(sailing));
        assertThat (plan.getRequirement(1), is(mapping));
    
        plan.removeRequirement(sailing);
        assertThat (plan.getNumberOfRequirements(), is(1));
        assertThat (plan.getRequirement(0), is(mapping));
    
        String outStr = plan.toString();
        assertThat (outStr, containsString("Trading"));
        assertThat (outStr, containsString("Mapping"));
        assertThat (outStr, not(containsString("Sailing")));
    
    }
    
    
    
    
    @Test
    public void ResearchPlanCopy() {
        Topic alphabet = new Topic("Alphabet", 15);
        Topic sailing = new Topic("Sailing", 36);
        Topic mapping = new Topic("Mapping", 30);
        Topic trading = new Topic("Trading", 35);
    
        ResearchPlan plan = new ResearchPlan(trading);
        plan.addRequirement(sailing);
        plan.addRequirement(mapping);
    
        ResearchPlan plan2 = (ResearchPlan)plan.clone();
        assertThat(plan2, is(plan));
        assertThat(plan2.compareTo(plan), is(0));
    
        plan2.addRequirement(alphabet);
        assertThat(plan2, not(is(plan)));
        assertThat(plan2.compareTo(plan), not(is(0)));
    
        assertThat (plan.getNumberOfRequirements(), is(2));
        assertThat (plan2.getNumberOfRequirements(), is(3));
        assertThat (plan.getRequirement(0), is(sailing));
        assertThat (plan2.getRequirement(0), is(sailing));
        assertThat (plan.getRequirement(1), is(mapping));
        assertThat (plan2.getRequirement(1), is(mapping));
        assertThat (plan2.getRequirement(2), is(alphabet));
    
        plan.removeRequirement(sailing);
        assertThat (plan.getNumberOfRequirements(), is(1));
        assertThat (plan2.getNumberOfRequirements(), is(3));
        assertThat (plan.getRequirement(0), is(mapping));
        assertThat (plan2.getRequirement(0), is(sailing));
        assertThat (plan2.getRequirement(1), is(mapping));
        assertThat (plan2.getRequirement(2), is(alphabet));
    }
    
    
    
    
    
    
}
