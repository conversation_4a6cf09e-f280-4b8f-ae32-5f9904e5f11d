package edu.odu.cs.cs361;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

public class Encyclopedia implements Iterable<Topic>, Cloneable {

	/**
	 * All of the plans in this encyclopedia.
	 */
	private List<ResearchPlan> plans;

	/**
	 * Check to see if a research plan already exists for this
	 * topic. If not, create one.  Return the created plan or
	 * existing one that was found.
	 */
	private ResearchPlan findOrAdd (String topicName) {
        for (ResearchPlan plan: plans) {
            if (plan.getTopic().getName().equals(topicName))
                return plan;
        }
        Topic topic = new Topic(topicName, 1);
        ResearchPlan plan = new ResearchPlan(topic);
        plans.add(plan);
        return plan;
    }




    /**
     *  Create a new encyclopedia.
     */
	public Encyclopedia() {
        plans = new ArrayList<>();
    }


    private class PlanIterator implements Iterator<Topic> {
        private Iterator<ResearchPlan> iter;

        public PlanIterator() {
            iter = plans.iterator();
        }
        @Override
        public boolean hasNext() {
            return iter.hasNext();
        }

        @Override
        public Topic next() {
            ResearchPlan plan = iter.next();
            return plan.getTopic();
        }
    }

	/**
	 * Provide access to the topics in the encyclopedia.
	 */
    public Iterator<Topic> iterator() {
        return new PlanIterator();
    }

    /**
     * @return number of plans/topics in the encyclopedia
     */
    public int size() { return plans.size(); }

	/**
	 * Adds a plan requirement to the encyclopedia. This is a pair of topics,
	 * possibly never seen before, such that one topic must have been researched
	 * before research can begin on the other.
	 *
	 * @param topic1  a topic, possibly never seen before
	 * @param requiredPriorTopic  another topic that must be researched
	 *                    before topic1.
	 */
	public void addPlanRequirement (Topic topic1, Topic requiredPriorTopic) {
        findOrAdd(requiredPriorTopic.getName());
        ResearchPlan plan1 = findOrAdd(topic1.getName());
        plan1.addRequirement(requiredPriorTopic);
    }

	/**
	 * Remove a topic from the encyclopedia, including any
	 * plan requirements in which it participates.
	 */
	public void removeTopic (Topic topic) {
        for (ResearchPlan plan: plans) {
            plan.removeRequirement (topic);
        }
        int i = 0;
        while (i < plans.size() && 
          !plans.get(i).getTopic().getName().equals(topic.getName())) {
            ++i;
        }
        if (i < plans.size()) {
            plans.remove(i);
        }
    }

	/**
	 * See if a topic is already in the catalog.
	 *
	 * @param topic a topic whose name is to be searched for
	 * @return true iff topic is in the catalog
	 */
	boolean containsTopic (Topic topic) {
        for (ResearchPlan plan: plans) {
            if(plan.getTopic().getName().equals(topic.getName())) {
                return true;
            }
        }
        return false;
    }

	/**
	 * Fetch a plan by topic name.
	 * @param topic a topic whose name is to be searched for
	 * @return the plan for the topic that same name.
	 * @throws NoSuchPlan if a plan for that topic does not exist.
	 */
	ResearchPlan getPlan (Topic topic) throws NoSuchPlan {
        for (ResearchPlan plan: plans) {
            if(plan.getTopic().getName().equals(topic.getName())) {
                return plan;
            }
        }
        throw new NoSuchPlan(topic.getName());
    }


	/**
	 * Read an encyclopedia from an input source.
	 *
	 * Input is repeated lines of
	 *
	 *   topic: prior topic 1, prior topic 2, ... , cost
	 *
	 * Topic names may be any mixture of alphanumeric characters and
	 * blanks, but must have at least one non-blank character, must not begin
	 * with a blank, and must not include the characters ',' or ':'.
	 *
	 * Leading blanks may appear before a topic name in the input, but are
	 * ignored.
	 *
	 * The end of input is signaled by the end of the input stream or by a
	 * line consisting solely of the string "---". (Three hyphens.)
	 *
	 * @param rdr the input source
	 * @throws IOException 
	 */
	public void read (BufferedReader input) throws IOException
    {
        String line = input.readLine();
        while (line != null && (!line.equals("---"))) {
            String[] parts = line.split(":");
            if (parts.length != 2)
                continue;
            String nameToResearch = parts[0].trim();
            parts = parts[1].split(",");
            String costStr = parts[parts.length-1].trim();
            int cost = Integer.parseInt(costStr);
            for (int i = 0; i < parts.length-1; ++i)
            {
                String requiredTopicName = parts[i].trim();
                ResearchPlan priorPlan = findOrAdd(requiredTopicName);
                Topic priorTopic = priorPlan.getTopic();
                ResearchPlan plan = findOrAdd(nameToResearch);
                plan.addRequirement(priorTopic);
            }
            ResearchPlan plan = findOrAdd(nameToResearch);
            plan.getTopic().setResearchCost(cost);
            line = input.readLine();
        }
    }


    @Override
	public boolean equals(Object obj) {
        if (obj instanceof Encyclopedia) {
            Encyclopedia enc = (Encyclopedia)obj;
            return plans.equals(enc.plans);
        } else
            return false;
    }


    @Override
    public Encyclopedia clone() {
        Encyclopedia e = new Encyclopedia();
        for (ResearchPlan plan: plans) {
            e.plans.add((ResearchPlan)plan.clone());
        }
        return e;
    }
}
