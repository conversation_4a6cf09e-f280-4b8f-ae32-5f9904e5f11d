package edu.odu.cs.cs361;

public class Topic implements Comparable<Topic> {

    private String name;
    private int researchCost;

    public Topic() {
        name = "";
        researchCost = 0;
    }

    public Topic(String theName, int theCost) {
        name = theName;
        researchCost = theCost;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the researchCost
     */
    public int getResearchCost() {
        return researchCost;
    }

    /**
     * @param researchCost the researchCost to set
     */
    public void setResearchCost(int researchCost) {
        this.researchCost = researchCost;
    }

    @Override
    public int compareTo(Topic topic) {
        return name.compareTo(topic.name);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof Topic) {
            Topic topic = (Topic) obj;
            return name.equals(topic.name);
        } else
            return false;
    }

    @Override
    public String toString() {
        return name + ": " + researchCost;
    }

}
