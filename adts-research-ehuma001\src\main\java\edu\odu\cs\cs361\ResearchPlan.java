package edu.odu.cs.cs361;

public class ResearchPlan implements Comparable<ResearchPlan>, Cloneable {

    //*** You should not need to change these data structures
    private Topic researchTopic;           ///< The topic to be researched
    private int numberOfRequirements; ///< How many requirements for this topic?
    private Topic[] requirements;      ///< an array of requirements. (max is 10)

	/**
	 * Create a new plan for a default topic (Topic()) and having no
	 * known prerequisites;
	 */
	public ResearchPlan() {
        researchTopic = new Topic();
        numberOfRequirements = 0;
        requirements = new Topic[10];
    }

	/**
	 * Create a new plan for researching a given topic and having no
	 * known prerequisites;
	 *
	 * @param topic the topic to be researched
	 */
	public ResearchPlan (Topic topic) {			// ************ DONE ************
		researchTopic = topic;
		numberOfRequirements = 0;
		requirements = new Topic[10];
    }


	/**
	 * @return the topic to be researched
	 */
	public Topic getTopic() {return researchTopic;}

	/**
	 * @return the number of known requirements for this plan.
	 */
	public int getNumberOfRequirements() { return numberOfRequirements; }

	/**
	 * Adds topic to the list of requirements for this plan.
	 * If this topic is already in the list, this has no effect.
	 *
	 * @param topic a required prior topic
	 */
	public void addRequirement(Topic topic){            // ************ DONE ************
		for (int i = 0; i < numberOfRequirements; i++) {
			if (requirements[i].equals(topic)) {
				return;
			}
		}

		if (numberOfRequirements < 10) {
			requirements[numberOfRequirements] = topic;
			numberOfRequirements++;
		}
    }

	/**
	 * Removes a topic from the list of requirements for this plan.
	 * If this topic is not already required, this has no effect.
	 *
	 * @param topic a prior required topic
	 */
	public void removeRequirement(Topic topic) {
		for (int i = 0; i < numberOfRequirements; i++) {
			if (requirements[i].equals(topic)) {
				for (int j = i; j < numberOfRequirements-1; j++) {
					requirements[j] = requirements[j+1];
				}
				numberOfRequirements--;
				requirements[numberOfRequirements] = null;
				return;
			}
		}
    }

	/**
	 * Get the name of the i_th requirement.
	 *
	 * @precondition 0 <= i && i < getNumberOfRequirements()
	 * @param i index of the requirement to retrieve
	 * @return a topic whose name indicates a required prior research topic.
	 *         The cost info in the returned topic is not necessarily accurate.
	 */
	Topic getRequirement(int i) {
        return requirements[i];
    }

    //*** Additional functions, as required, here.
	
	public Object clone() {
		ResearchPlan cloned = new ResearchPlan(new Topic(this.researchTopic.getName(), this.researchTopic.getResearchCost()));

		for (int i = 0; i < this.numberOfRequirements; i++) {
			cloned.addRequirement(new Topic(this.requirements[i].getName(), this.requirements[i].getResearchCost()));
		}
		return cloned;
	}
	@Override
	public boolean equals(Object obj) {
		if (obj instanceof ResearchPlan) {
			ResearchPlan other = (ResearchPlan) obj;
		
		
		if (!this.researchTopic.equals(other.researchTopic)) {
			return false;
		}

		if (this.numberOfRequirements != other.numberOfRequirements) {
			return false;
		}

		for (int i = 0; i < this.numberOfRequirements; i++) {
			if (!this.requirements[i].equals(other.requirements[i])) {
				return false;
			}
		}
		return true;
	}
	return false;
}

	public int compareTo(ResearchPlan other) {
		int topicComparison = this.researchTopic.compareTo(other.researchTopic);
		if (topicComparison != 0) {
			return topicComparison;
		}

		// If topics are equal, compare by number of requirements
		if (this.numberOfRequirements != other.numberOfRequirements) {
			return this.numberOfRequirements - other.numberOfRequirements;
		}

		// If same number of requirements, compare each requirement
		for (int i = 0; i < this.numberOfRequirements; i++) {
			int reqComparison = this.requirements[i].compareTo(other.requirements[i]);
			if (reqComparison != 0) {
				return reqComparison;
			}
		}

		return 0;
	}

	@Override
	public String toString() {
		StringBuffer buf = new StringBuffer();
		buf.append(researchTopic.toString());

	if (numberOfRequirements > 0) {
		buf.append("(requires: ");
		for (int i = 0; i < numberOfRequirements; i++) {
			if (i > 0) {
				buf.append(", ");
			}
			buf.append(requirements[i].toString());
		}
		buf.append(")");
	}
		return buf.toString();
}
}


